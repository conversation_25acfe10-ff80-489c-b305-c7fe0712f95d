# For more on configuring pre-commit hooks (see https://pre-commit.com/)

# TODO: remove global exclusion of tests when testing overhaul is complete
exclude: "^tests/.*"


default_language_version:
  python: python3.11

repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v3.2.0
  hooks:
  - id: check-yaml
    args: [--unsafe]
  - id: check-json
    exclude: ^.devcontainer/
  - id: end-of-file-fixer
  - id: trailing-whitespace
  - id: check-case-conflict
- repo: https://github.com/asottile/reorder_python_imports
  rev: v3.9.0
  hooks:
  -  id: reorder-python-imports
- repo: https://github.com/astral-sh/ruff-pre-commit
  # Ruff version.
  rev: v0.1.7
  hooks:
    # Run the linter.
    - id: ruff
      args:
      - "--line-length=99"
      - "--fix"
    # Run the formatter.
    - id: ruff-format
      args:
      - "--line-length=99"
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v0.782
  hooks:
  - id: mypy
    args: [--show-error-codes, --ignore-missing-imports]
    files: ^dbt/adapters/.*
    language: system
  - id: mypy
    alias: mypy-check
    stages: [manual]
    args: [--show-error-codes, --pretty, --ignore-missing-imports]
    files: ^dbt/adapters
    language: system
