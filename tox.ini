[tox]
skipsdist = True
envlist = py{38,39,310,311,312}

[testenv:{unit,py38,py39,py310,py311,py312,py}]
description = unit testing
skip_install = True
passenv = *
commands = {envpython} -m pytest {posargs} tests/unit
deps =
  -rdev-requirements.txt
  -e.

[testenv:{functional,py38,py39,py310,py311,py312,py}]
description = adapter functional testing
skip_install = True
passenv = *
commands = {envpython} -m pytest {posargs} tests/functional/adapter
deps =
  -rdev-requirements.txt
  -e.

[testenv:{filebased,py38,py39,py310,py311,py312,py}]
description = adapter functional testing using file-based DBs
skip_install = True
passenv = *
commands = {envpython} -m pytest --profile=file {posargs} tests/functional/adapter
deps =
  -rdev-requirements.txt
  -e.

[testenv:{buenavista,py39}]
description = adapter functional testing using a Buena Vista server
skip_install = True
passenv = *
commands = {envpython} -m pytest --profile=buenavista {posargs} tests/functional/adapter
deps =
  -rdev-requirements.txt
  -e.

[testenv:{md,py39}]
description = adapter function testing using MotherDuck
skip_install = True
passenv = *
commands = {envpython} -m pytest --profile=md --maxfail=2 {posargs} tests/functional/plugins/motherduck tests/functional/adapter
deps =
  -rdev-requirements.txt
  -e.[md]

[testenv:{fsspec,py38,py39,py310,py311,py312,py}]
description = adapter fsspec testing
skip_install = True
passenv = *
commands = {envpython} -m pytest {posargs} tests/functional/fsspec
deps =
  -rdev-requirements.txt
  -e.

[testenv:{plugins,py38,py39,py310,py311,py312,py}]
description = adapter plugin testing
skip_install = True
passenv = *
commands = {envpython} -m pytest {posargs} --profile=file tests/functional/plugins
deps =
  -rdev-requirements.txt
  -e.

[testenv:{nightly,py38,py39,py310,py311,py312,py}]
description = duckdb nightly release testing
skip_install = True
passenv = *
commands =
  {envpython} -m pip install --upgrade --pre duckdb
  {envpython} -m pip show duckdb
  {envpython} -m pytest {posargs} --profile=nightly tests/unit tests/functional/adapter
deps =
  -rdev-requirements.txt
  -e.
