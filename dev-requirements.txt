# install latest changes in dbt-core + dbt-tests-adapter
# git+https://github.com/dbt-labs/dbt-core.git#egg=dbt-core&subdirectory=core
# git+https://github.com/dbt-labs/dbt-adapters.git#subdirectory=dbt-tests-adapter

dbt-tests-adapter==1.16.0

boto3
mypy-boto3-glue
pandas
pyarrow==21.0.0
buenavista==0.5.0
bumpversion
flaky
freezegun==1.5.3
fsspec
gspread
ipdb
mypy==1.17.0
openpyxl
pip-tools
pre-commit
psycopg2-binary
psycopg[binary]
pyiceberg
pytest
pytest-dotenv
pytest-logbook
pytest-csv
pytest-xdist
pytest-mock
testcontainers[postgres]
pytz
ruff
sqlalchemy
tox>=3.13
twine
wheel
deltalake
