[metadata]
name = dbt-duckdb
author = <PERSON>
author_email = josh<PERSON><EMAIL>
url = https://github.com/jwills/dbt-duckdb
summary = The duckdb adapter plugin for dbt (data build tool)
description_file = README.md
long_description_content_type = text/markdown
license = Apache-2
classifier =
    Development Status :: 5 - Production/Stable
    License :: OSI Approved :: Apache Software License
    Operating System :: Microsoft :: Windows
    Operating System :: MacOS :: MacOS X
    Operating System :: POSIX :: Linux
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
keywords =
    setup
    distutils

[options]
install_requires=
    dbt-common>=1,<2
    dbt-adapters>=1,<2
    duckdb>=1.0.0
    # add dbt-core to ensure backwards compatibility of installation, this is not a functional dependency
    dbt-core>=1.8.0
python_requires = >=3.9
include_package_data = True
packages = find_namespace:

[options.packages.find]
include =
    dbt
    dbt.*

[build-system]
requires = ["setuptools >= 61.2", "pbr>=1.9"]

[extras]
glue =
    boto3
    mypy-boto3-glue
md =
    duckdb==1.3.1

[files]
packages =
    dbt-duckdb
