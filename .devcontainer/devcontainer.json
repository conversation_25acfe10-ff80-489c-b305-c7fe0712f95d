{
	"name": "Python 3",
	"build": {
		"dockerfile": "Dockerfile",
		"context": "..",
		"args": {
			// Update 'VARIANT' to pick a Python version: 3, 3.10, 3.9, 3.8, 3.7, 3.6
			// Append -bullseye or -buster to pin to an OS version.
			// Use -bullseye variants on local on arm64/Apple Silicon.
			"VARIANT": "3.11",
			// Options
			"NODE_VERSION": "none"
		}
	},
	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Set *default* container specific settings.json values on container create.
			"settings": {
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.testing.pytestEnabled": true,
				"python.testing.unittestEnabled": false,
				"python.linting.enabled": true,
				"python.linting.flake8Enabled": true,
				"python.linting.mypyEnabled": true,
				"python.linting.pylintEnabled": false,
				"python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
				"python.formatting.provider": "black",
				"python.formatting.blackPath": "/usr/local/py-utils/bin/black",
				"python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
				"python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
				"python.linting.flake8Path": "/usr/local/py-utils/bin/flake8",
				"python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
				"python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
				"python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
				"python.linting.pylintPath": "/usr/local/py-utils/bin/pylint",
				"[python]": {
					"editor.defaultFormatter": "ms-python.python",
					"editor.formatOnSave": true,
					"editor.tabSize": 4,
					"editor.codeActionsOnSave": {
						"source.organizeImports": true
					}
				}
			},
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"ms-python.python",
				"ms-python.vscode-pylance"
			]
		}
	},
	// "features": {
	// 	// Allow the devcontainer to run host docker commands, see https://github.com/devcontainers/templates/tree/main/src/docker-outside-of-docker
	// 	"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {
	// 		"enableNonRootDocker": true
	// 	}
	// },
	// "mounts": [
    	// 	"source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
  	// ],
	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],
	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "pip3 install --user -r requirements.txt",
	// Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode",
	"workspaceFolder": "/workspaces/dbt-duckdb",
	"postCreateCommand": "pip install -e . && pip install -r dev-requirements.txt"
}
