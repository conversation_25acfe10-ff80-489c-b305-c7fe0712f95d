# **what?**
# Runs unit tests and functional tests using the latest nightly DuckDB build.
# Any tests that use community extensions are skipped because these are not released nightly.

# **why?**
# Ensure code for dbt is compatible with the bleeding edge version of DuckDB.

# **when?**
# This will run nightly, after DuckDB releases its nightly build.

name: Tests and Code Checks (DuckDB nightly)

on:
  schedule:
    - cron: '0 0 * * *' # every 24 hours, top of the hour
  workflow_dispatch:

permissions: read-all

defaults:
  run:
    shell: bash

jobs:
  nightly:
    name: nightly test / python ${{ matrix.python-version }}

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']

    env:
      TOXENV: "nightly"
      PYTEST_ADDOPTS: "-v --color=yes --csv unit_results.csv"
      S3_MD_ORG_KEY: ${{ secrets.S3_MD_ORG_KEY }}
      S3_MD_ORG_REGION: ${{ secrets.S3_MD_ORG_REGION }}
      S3_MD_ORG_SECRET: ${{ secrets.S3_MD_ORG_SECRET }}

    steps:
      - name: Check out the repository
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install python dependencies
        run: |
          python -m pip install tox
          python -m pip --version
          tox --version

      - name: Run tox
        run: tox

      - name: Get current date
        if: always()
        id: date
        run: echo "date=$(date +'%Y-%m-%dT%H_%M_%S')" >> $GITHUB_OUTPUT #no colons allowed for artifacts

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: unit_results_${{ matrix.python-version }}-${{ steps.date.outputs.date }}.csv
          path: unit_results.csv
